import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { generateChatResponse } from "./llm";
import { insertChatSchema, insertMessageSchema } from "@shared/schema";
import { ZodError } from "zod";
import { generateWeb3ChatTitle } from "./chatTitleGenerator";

export async function registerRoutes(app: Express): Promise<Server> {
  // Create Nebula session
  app.post("/api/nebula/create-session", async (req, res) => {
    try {
      const { sendNebulaMessage } = await import("./nebulaClient");

      // Send initial message to create session
      const nebulaResponse = await sendNebulaMessage(
        "Hello, I am ready to chat about blockchain development!",
        undefined, // Let Nebula create new session
        false
      );

      return res.json({
        sessionId: nebulaResponse.session_id,
        message: "Session created successfully",
      });
    } catch (error) {
      console.error("Failed to create Nebula session:", error);
      return res.status(500).json({ error: "Failed to create Nebula session" });
    }
  });

  // Proxy Nebula chat API calls (for client-side usage)
  app.post("/api/nebula/chat", async (req, res) => {
    try {
      const { sendNebulaMessage } = await import("./nebulaClient");
      const { message, session_id, stream, contextFilter } = req.body;

      if (!message) {
        return res.status(400).json({ error: "Message is required" });
      }

      // Handle regular responses (both streaming and non-streaming return JSON)
      const nebulaResponse = await sendNebulaMessage(
        message,
        session_id,
        stream || false,
        contextFilter
      );

      // Transform response to match client expectations
      return res.json({
        response: nebulaResponse.message,
        session_id: nebulaResponse.session_id,
        request_id: nebulaResponse.request_id,
        actions: nebulaResponse.actions,
        transactions: nebulaResponse.transactions,
      });
    } catch (error) {
      console.error("Failed to send Nebula message:", error);
      return res
        .status(500)
        .json({ error: "Failed to send message to Nebula" });
    }
  });

  // New streaming endpoint for Server-Sent Events
  app.post("/api/nebula/chat/stream", async (req, res) => {
    try {
      const { sendNebulaMessageStream } = await import("./nebulaClient");
      const { message, session_id, contextFilter } = req.body;

      if (!message) {
        return res.status(400).json({ error: "Message is required" });
      }

      // Set headers for Server-Sent Events
      res.writeHead(200, {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
        Connection: "keep-alive",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Cache-Control",
      });

      try {
        await sendNebulaMessageStream(
          message,
          session_id,
          contextFilter,
          (chunk: string, isComplete: boolean, metadata?: any) => {
            if (isComplete) {
              // Send final metadata
              res.write(
                `data: ${JSON.stringify({
                  type: "complete",
                  session_id: metadata?.session_id,
                  request_id: metadata?.request_id,
                  actions: metadata?.actions,
                  transactions: metadata?.transactions,
                })}\n\n`
              );
              res.end();
            } else {
              // Send chunk
              res.write(
                `data: ${JSON.stringify({
                  type: "chunk",
                  content: chunk,
                })}\n\n`
              );
            }
          }
        );
      } catch (error) {
        console.error("Streaming error:", error);
        res.write(
          `data: ${JSON.stringify({
            type: "error",
            error: "Streaming failed",
          })}\n\n`
        );
        res.end();
      }
    } catch (error) {
      console.error("Failed to send streaming Nebula message:", error);
      res.write(
        `data: ${JSON.stringify({
          type: "error",
          error: "Failed to send message to Nebula",
        })}\n\n`
      );
      res.end();
    }
  });

  // Get all chats
  app.get("/api/chats", async (req, res) => {
    try {
      const walletAddress = req.query.walletAddress as string;
      const chats = await storage.getChats(walletAddress);
      return res.json(chats);
    } catch (error) {
      return res.status(500).json({ message: "Failed to fetch chats" });
    }
  });

  // Create a new chat
  app.post("/api/chats", async (req, res) => {
    try {
      const parsedData = insertChatSchema.parse(req.body);
      const chat = await storage.createChat(parsedData);
      return res.status(201).json(chat);
    } catch (error) {
      if (error instanceof ZodError) {
        return res
          .status(400)
          .json({ message: "Invalid chat data", errors: error.errors });
      }
      return res.status(500).json({ message: "Failed to create chat" });
    }
  });

  // Delete a chat
  app.delete("/api/chats/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid chat ID" });
      }
      const walletAddress = req.query.walletAddress as string;
      const success = await storage.deleteChat(id, walletAddress);
      if (!success) {
        return res
          .status(404)
          .json({ message: "Chat not found or access denied" });
      }
      return res.status(200).json({ message: "Chat deleted" });
    } catch (error) {
      return res.status(500).json({ message: "Failed to delete chat" });
    }
  });

  // Get messages for a chat
  app.get("/api/chats/:id/messages", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid chat ID" });
      }
      const walletAddress = req.query.walletAddress as string;
      const chat = await storage.getChat(id, walletAddress);
      if (!chat) {
        return res
          .status(404)
          .json({ message: "Chat not found or access denied" });
      }
      const messages = await storage.getMessages(id);
      return res.json(messages);
    } catch (error) {
      return res.status(500).json({ message: "Failed to fetch messages" });
    }
  });

  // Create a new message and get AI response
  app.post("/api/chats/:id/messages", async (req, res) => {
    try {
      const chatId = parseInt(req.params.id);
      if (isNaN(chatId)) {
        return res.status(400).json({ message: "Invalid chat ID" });
      }

      const {
        walletAddress,
        metadata,
        sessionId: requestSessionId,
        account,
      } = req.body;
      const chat = await storage.getChat(chatId, walletAddress);
      if (!chat) {
        return res
          .status(404)
          .json({ message: "Chat not found or access denied" });
      }

      // Parse and create user message
      const messageData = { ...req.body, chatId, role: "user" };
      const parsedData = insertMessageSchema.parse(messageData);
      const userMessage = await storage.createMessage(parsedData);

      // Check if this is the first message and update chat title
      const chatHistory = await storage.getMessages(chatId);
      if (chatHistory.length === 1) {
        // Only the user message we just created
        const newTitle = generateWeb3ChatTitle(parsedData.content);
        await storage.updateChatTitle(chatId, newTitle, walletAddress);
      }
      const chainId = metadata?.chainId || "1"; // Default to Ethereum if not specified
      let sessionId = requestSessionId; // Get session from request

      // If no session ID provided, create a new one for this conversation
      if (!sessionId) {
        try {
          const { sendNebulaMessage } = await import("./nebulaClient");
          // Create a new session by sending an initial message to Nebula
          const nebulaResponse = await sendNebulaMessage(
            "Hello, I am ready to chat about blockchain development!",
            undefined, // Let Nebula create new session
            false
          );
          sessionId = nebulaResponse.session_id;
          console.log("Created new Nebula session:", sessionId);
        } catch (error) {
          console.error("Failed to create Nebula session:", error);
          // Continue without session ID - the LLM will handle fallback
          sessionId = `fallback_${Date.now()}`;
        }
      }

      try {
        const aiResponse = await generateChatResponse(
          chatHistory,
          chainId,
          sessionId,
          walletAddress
        );

        // Save assistant message
        const assistantMessageData = {
          chatId,
          role: "assistant",
          content: aiResponse.text,
          metadata: {
            chainId,
            source: aiResponse.source,
            executionTime: aiResponse.executionTime,
            blockchainData: aiResponse.blockchainData,
          },
        };

        const assistantMessage = await storage.createMessage(
          assistantMessageData
        );

        return res.status(201).json({
          userMessage,
          assistantMessage,
        });
      } catch (error) {
        // If LLM fails, still save the user message but return an error
        return res.status(500).json({
          userMessage,
          error: "Failed to generate assistant response",
        });
      }
    } catch (error) {
      if (error instanceof ZodError) {
        return res
          .status(400)
          .json({ message: "Invalid message data", errors: error.errors });
      }
      return res.status(500).json({ message: "Failed to process message" });
    }
  });

  // Get available chains
  app.get("/api/chains", async (_req, res) => {
    try {
      const chains = await storage.getChains();
      return res.json(chains);
    } catch (error) {
      return res.status(500).json({ message: "Failed to fetch chains" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
