// Nebula API client for server-side integration
// Uses direct API calls with wallet and chain context
// Based on: https://portal.thirdweb.com/nebula/api-reference/chat

const NEBULA_API_BASE = "https://nebula-api.thirdweb.com";

export interface NebulaMessage {
  message: string;
  stream?: boolean;
  session_id?: string;
}

export interface NebulaResponse {
  message: string;
  actions?: Array<{
    session_id: string;
    request_id: string;
    type: string;
    source: string;
    data: string;
  }>;
  session_id: string;
  request_id: string;
  transactions?: any[];
}

export interface NebulaContextFilter {
  walletAddresses?: string[];
  chainIds?: number[];
  contractAddresses?: string[];
}

/**
 * Send a message to Nebula Chat API with wallet and chain context
 */
export async function sendNebulaMessage(
  message: string,
  sessionId?: string,
  stream: boolean = false,
  contextFilter?: NebulaContextFilter
): Promise<NebulaResponse> {
  // Get the secret key from environment
  const secretKey = process.env.THIRDWEB_SECRET_KEY;

  if (!secretKey) {
    throw new Error("ThirdWeb secret key is required for Nebula API");
  }

  // Enhance message with context information
  let enhancedMessage = message;
  if (contextFilter) {
    const contextParts = [];

    if (
      contextFilter.walletAddresses &&
      contextFilter.walletAddresses.length > 0
    ) {
      contextParts.push(`Wallet: ${contextFilter.walletAddresses[0]}`);
    }

    if (contextFilter.chainIds && contextFilter.chainIds.length > 0) {
      const chainNames: Record<number, string> = {
        1: "Ethereum Mainnet",
        137: "Polygon",
        56: "Binance Smart Chain",
        11155111: "Sepolia Testnet",
        80002: "Amoy Testnet",
      };
      const chainName =
        chainNames[contextFilter.chainIds[0]] ||
        `Chain ${contextFilter.chainIds[0]}`;
      contextParts.push(`Network: ${chainName}`);
    }

    if (contextParts.length > 0) {
      enhancedMessage = `[Context: ${contextParts.join(", ")}] ${message}`;
    }
  }

  const payload: NebulaMessage = {
    message: enhancedMessage,
    stream,
  };

  // Only include session_id if provided (let Nebula create new sessions)
  if (sessionId) {
    payload.session_id = sessionId;
  }

  try {
    const response = await fetch(`${NEBULA_API_BASE}/chat`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-secret-key": secretKey,
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Nebula API error: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    console.log("Nebula API Response:", JSON.stringify(data, null, 2));
    return data;
  } catch (error) {
    console.error("Error calling Nebula API:", error);
    throw error;
  }
}

/**
 * Send a streaming message to Nebula Chat API with wallet and chain context
 */
export async function sendNebulaMessageStream(
  message: string,
  sessionId?: string,
  contextFilter?: NebulaContextFilter,
  onChunk?: (chunk: string, isComplete: boolean, metadata?: any) => void
): Promise<void> {
  console.log("sendNebulaMessageStream called with:", {
    message,
    sessionId,
    contextFilter,
  });

  // Get the secret key from environment
  const secretKey = process.env.THIRDWEB_SECRET_KEY;

  if (!secretKey) {
    throw new Error("ThirdWeb secret key is required for Nebula API");
  }

  // Enhance message with context information
  let enhancedMessage = message;
  if (contextFilter) {
    const contextParts = [];

    if (
      contextFilter.walletAddresses &&
      contextFilter.walletAddresses.length > 0
    ) {
      contextParts.push(`Wallet: ${contextFilter.walletAddresses[0]}`);
    }

    if (contextFilter.chainIds && contextFilter.chainIds.length > 0) {
      const chainNames: Record<number, string> = {
        1: "Ethereum Mainnet",
        137: "Polygon",
        56: "Binance Smart Chain",
        11155111: "Sepolia Testnet",
        80002: "Amoy Testnet",
      };
      const chainName =
        chainNames[contextFilter.chainIds[0]] ||
        `Chain ${contextFilter.chainIds[0]}`;
      contextParts.push(`Network: ${chainName}`);
    }

    if (contextParts.length > 0) {
      enhancedMessage = `[Context: ${contextParts.join(", ")}] ${message}`;
    }
  }

  const payload: NebulaMessage = {
    message: enhancedMessage,
    stream: true,
  };

  // Only include session_id if provided (let Nebula create new sessions)
  if (sessionId) {
    payload.session_id = sessionId;
  }

  try {
    const response = await fetch(`${NEBULA_API_BASE}/chat`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-secret-key": secretKey,
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Nebula API error: ${response.status} - ${errorText}`);
    }

    // Check if the response is actually streaming
    const contentType = response.headers.get("content-type");
    console.log("Nebula API response content-type:", contentType);

    if (
      (contentType?.includes("text/event-stream") ||
        contentType?.includes("text/plain")) &&
      response.body
    ) {
      // Handle Server-Sent Events or streaming text
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = "";
      let finalMetadata: any = {};

      try {
        while (true) {
          const { done, value } = await reader.read();

          if (done) {
            // Send completion signal with metadata
            onChunk?.("", true, finalMetadata);
            break;
          }

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split("\n");
          buffer = lines.pop() || ""; // Keep incomplete line in buffer

          for (const line of lines) {
            if (line.startsWith("data: ")) {
              try {
                const data = JSON.parse(line.slice(6));

                // Handle different types of streaming data
                if (data.type === "chunk" && data.content) {
                  onChunk?.(data.content, false);
                } else if (data.type === "complete") {
                  finalMetadata = {
                    session_id: data.session_id,
                    request_id: data.request_id,
                    actions: data.actions,
                    transactions: data.transactions,
                  };
                } else if (data.message) {
                  // Handle direct message chunks
                  onChunk?.(data.message, false);
                  if (data.session_id) {
                    finalMetadata.session_id = data.session_id;
                  }
                  if (data.request_id) {
                    finalMetadata.request_id = data.request_id;
                  }
                  if (data.actions) {
                    finalMetadata.actions = data.actions;
                  }
                  if (data.transactions) {
                    finalMetadata.transactions = data.transactions;
                  }
                }
              } catch (parseError) {
                console.warn("Failed to parse streaming data:", line);
              }
            } else if (line.trim()) {
              // Handle plain text streaming (each line is a chunk)
              onChunk?.(line, false);
            }
          }
        }
      } finally {
        reader.releaseLock();
      }
    } else {
      // Handle regular JSON response (Nebula might not support streaming)
      console.log(
        "Nebula API returned JSON instead of streaming, simulating streaming..."
      );
      const data = await response.json();
      console.log("Nebula API Response:", JSON.stringify(data, null, 2));

      // Simulate streaming by breaking the message into chunks
      if (data.message) {
        const words = data.message.split(" ");
        for (let i = 0; i < words.length; i++) {
          const chunk = words.slice(0, i + 1).join(" ");
          onChunk?.(chunk, false);
          // Small delay to simulate streaming
          await new Promise((resolve) => setTimeout(resolve, 50));
        }
      }

      // Send completion with metadata
      onChunk?.("", true, {
        session_id: data.session_id,
        request_id: data.request_id,
        actions: data.actions,
        transactions: data.transactions,
      });
    }
  } catch (error) {
    console.error("Error calling Nebula streaming API:", error);
    throw error;
  }
}

/**
 * Generate a new session ID for Nebula chat
 */
export function generateSessionId(): string {
  // Generate a proper UUID v4 format
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0;
    const v = c == "x" ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}
