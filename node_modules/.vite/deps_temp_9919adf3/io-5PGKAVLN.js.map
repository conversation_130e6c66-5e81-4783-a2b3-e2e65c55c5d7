{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.legacynetwork/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.legacynetwork\",\n  name: \"Legacy Wallet\",\n  homepage: \"https://www.legacynetwork.io/\",\n  image_id: \"ed181b1b-e4c0-4a2e-4a4c-f380a9f13c00\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/us/app/legacy-wallet/id6443578674\",\n    android:\n      \"https://play.google.com/store/apps/details?id=io.legacynetwork.app\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome:\n      \"https://chrome.google.com/webstore/detail/legacy-wallet/ammjlinfekkoockogfhdkgcohjlbhmff\",\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"legacy://network.io\",\n    universal: null,\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QACE;IACF,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}