import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/wallets/__generated__/wallet/pro.tokenpocket/image.js
var image = "data:image/webp;base64,UklGRkAFAABXRUJQVlA4IDQFAABwHACdASqAAIAAPm00l0ckIyIhKJLaOIANiWQA1bXVu//sv5FeypT36l/Nf0TxoqJes79r6AP8Z7CPMA/UzpI+YD9lf2q94P+tfsV7hv8N/evYK/ofmgewR6BXlz+zL+5P7Z4hw+2VlEZksBbyW31G0KjruR3I6oHeKUo0i6YE9bEn+HEKuryzSOi/qinAsJZx2OPFo8oWT2OMUPqlE/9tD+FXljm6bNBL9cVLfE3+6G7K8M2NyD8/OIwwfxODP0udZudbWSSbyYdek0tMz9GLfK+i86oapAILbjN448uk1GTL+TqYlyIi3KnDsAD+4Ll/9o++o++o+maf/PsOi7GwAAAAawSf/UtD3TAd12X6O26udhspjQtOh2axOtQwz6gz+XnKOQy8/pqwFp9ac5D1OEllouGFE8JOZbbEds1DdCxbUakQeT0JAayRA/VCQAE6eWuRIbSfXp5bpC0V2TCPDnxnzZ6AXkhlarSJ5GIbT/B4ATOT7eDburUvrmTN6JZmuCWiEJi8Ddsm8WYpR6t7iB/ZhtZaeWTxpGdd5q73Xip8QXXZgDkgXNPTvL0SI07KBIMf2omcrjQED5oUmVP/wZQqMguTccDmKiSwppM0n8ldtr7XeHEGl+hMGm+W4DR04chTLjGjm9e6fIdRKWyXRktw7r1N2ks9B8H7iPnIFxqeux/TD8Dqrvp7QSwojK1dzO8t4b0TkYtwQjUrGpmACMso2KCQMwnv5iDY1iBEN+gomdj5efrC534hLgTNwEU3R748Hli8wQK3NU8ok5cL7AUIR0zjRPKuqUZsI8MdcBv8bd2wuJ5PNHO2oa/8NbhcOpfTUNOU3mFgbCnqy/DH8LKvD7rhXe/rru3d5w/c5jvk9wQRdSR99U0B/x9KCeeNF2XBcyi7D6BD4jWUlcZWmAA8jZ33UXj+qXbJeaUllQkv/z1FwZ4GGJdziDYITtlAqE8yRq19h3r04sn9tNVBE0OwgVudif0Y7KewJlq+yMjHu/K3SH0UTcM2lDWbPzU5Qwd4kUK6Oul7hhDmABkv25BRWlqRxdONTHG3mv87ThkLoFefSJOaG4EUDmeqOMiiZuVeWGwN292eP8mRW9+ZPbs69wVw7lJY5RwPhpYt4q4roxBRz3udhWYt45A357veOxk3fLnx2OaTchSVhqYQo5SeAK4lyARQp80LCe7puR/l6K0N5ux+2nk9n7JCTHRMudTreWxO22u0lrVRO31XNXlZvSMhDClbwMJd+8jzSx5a/W2/ksprPyDpNp7GmFYgkv6vFwOVcy+cElabRW9d3p9gmGZNTP5Hd89F7cTzohrz3YNSrWR3byA5Qf5tRdJ7VYlgFjAAcd5mqZvvp2a0+Ot3kFUphsqPDgAY6dmuIhVdsMu0el94cH6p/TiCOlww8DmO4gtj4m91Lz03xXHS/N8wQy+rAiaZMi+egU3gmHGI6c4P0VYZ9r06hb32SSqYpJNc0r6TYqvN78PrOfU3L0SLnPUaxDBc4G4kyVnnVaOj/mfkshS5rAd0W03TTo5oH4BA5xG9Y7VAR3S6GdesdCtpKl34m6HMCIKmjWfgc/xS35++VU4XgenIQJ+pJZPFUu+1uKoGuOQc7GBjrKaKQrbUaIiNNVf9YUgk5dJk3Zfa4DZPzP58VpAlA6VRV4y9KzzNfsa2zGmjRcvwPST6VfRhHRRLSRxVkoDXtKC7d1GBOStZ5qEZJFQN1jxvwx+B3ssEOC+HOn4b2uOYQiHEqrtSvfPpbw52N+WIa2YkAAAAAAA=";
var image_default = image;
export {
  image_default as default
};
//# sourceMappingURL=image-YGBNLPGT.js.map
