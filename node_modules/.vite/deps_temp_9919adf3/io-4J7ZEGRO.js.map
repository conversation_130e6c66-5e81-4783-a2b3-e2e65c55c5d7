{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.universaleverything/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.universaleverything\",\n  name: \"UniversalProfiles\",\n  homepage: \"https://universaleverything.io\",\n  image_id: \"4537fb11-ada3-4009-37c2-c70ed4b46300\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/us/app/universalprofiles/id6702018631\",\n    android:\n      \"https://play.google.com/store/apps/details?id=io.universaleverything.universalprofiles\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"io.universaleverything.universalprofiles://wallet-connect\",\n    universal: null,\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}