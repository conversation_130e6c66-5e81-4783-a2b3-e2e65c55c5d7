{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.owallet/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.owallet\",\n  name: \"<PERSON><PERSON>alle<PERSON>\",\n  homepage: \"https://owallet.io\",\n  image_id: \"68489978-9f79-47f4-fd59-86a29df9bf00\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/app/owallet/id1626035069\",\n    android: \"https://play.google.com/store/apps/details?id=com.io.owallet\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome:\n      \"https://chrome.google.com/webstore/detail/owallet/hhejbopdnpbjgomhpmegemnjogflenga\",\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"owallet://\",\n    universal: null,\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QACE;IACF,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}