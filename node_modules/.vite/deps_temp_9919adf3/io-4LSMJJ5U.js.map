{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.kriptomat/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.kriptomat\",\n  name: \"<PERSON>riptomat\",\n  homepage: \"https://kriptomat.io/web3/\",\n  image_id: \"774110aa-70f6-4d0c-210f-ab434838fa00\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/app/id1440135740\",\n    android: \"https://play.google.com/store/apps/details?id=io.kriptomat.app\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"kriptomatapp://wallet-connect\",\n    universal: null,\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}