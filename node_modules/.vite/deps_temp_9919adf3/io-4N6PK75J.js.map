{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.pitaka/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.pitaka\",\n  name: \"<PERSON><PERSON>\",\n  homepage: \"https://pitaka.io\",\n  image_id: \"c816aeae-e0d1-4c52-f37a-efde6df1ee00\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/ph/app/pitaka-blockchain-wallet/id1644341925\",\n    android: \"https://play.google.com/store/apps/details?id=com.pitakamobile\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"pitaka://\",\n    universal: \"https://app.pitaka.io\",\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}