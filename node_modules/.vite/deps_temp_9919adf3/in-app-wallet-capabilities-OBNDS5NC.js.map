{"version": 3, "sources": ["../../thirdweb/src/wallets/in-app/core/eip5972/in-app-wallet-capabilities.ts"], "sourcesContent": ["import type { Wallet } from \"../../../interfaces/wallet.js\";\n\n/**\n * @internal\n */\nexport function inAppWalletGetCapabilities(args: {\n  wallet: Wallet<\"inApp\" | \"embedded\">;\n}) {\n  const { wallet } = args;\n\n  const chain = wallet.getChain();\n  if (chain === undefined) {\n    return {\n      message: `Can't get capabilities, no active chain found for wallet: ${wallet.id}`,\n    };\n  }\n\n  const account = wallet.getAccount();\n\n  const config = wallet.getConfig();\n  const sponsorGas =\n    config?.smartAccount && \"sponsorGas\" in config.smartAccount\n      ? config.smartAccount.sponsorGas\n      : config?.executionMode\n        ? config.executionMode.mode === \"EIP4337\" &&\n          config.executionMode.smartAccount &&\n          \"sponsorGas\" in config.executionMode.smartAccount\n          ? config.executionMode.smartAccount.sponsorGas\n          : config.executionMode.mode === \"EIP7702\"\n            ? config.executionMode.sponsorGas\n            : false\n        : false;\n\n  return {\n    [chain.id]: {\n      paymasterService: {\n        supported: sponsorGas,\n      },\n      atomic: {\n        status:\n          account?.sendBatchTransaction !== undefined\n            ? \"supported\"\n            : \"unsupported\",\n      },\n    },\n  };\n}\n"], "mappings": ";;;AAKM,SAAU,2BAA2B,MAE1C;AACC,QAAM,EAAE,OAAM,IAAK;AAEnB,QAAM,QAAQ,OAAO,SAAQ;AAC7B,MAAI,UAAU,QAAW;AACvB,WAAO;MACL,SAAS,6DAA6D,OAAO,EAAE;;EAEnF;AAEA,QAAM,UAAU,OAAO,WAAU;AAEjC,QAAM,SAAS,OAAO,UAAS;AAC/B,QAAM,cACJ,iCAAQ,iBAAgB,gBAAgB,OAAO,eAC3C,OAAO,aAAa,cACpB,iCAAQ,iBACN,OAAO,cAAc,SAAS,aAC9B,OAAO,cAAc,gBACrB,gBAAgB,OAAO,cAAc,eACnC,OAAO,cAAc,aAAa,aAClC,OAAO,cAAc,SAAS,YAC5B,OAAO,cAAc,aACrB,QACJ;AAER,SAAO;IACL,CAAC,MAAM,EAAE,GAAG;MACV,kBAAkB;QAChB,WAAW;;MAEb,QAAQ;QACN,SACE,mCAAS,0BAAyB,SAC9B,cACA;;;;AAId;", "names": []}