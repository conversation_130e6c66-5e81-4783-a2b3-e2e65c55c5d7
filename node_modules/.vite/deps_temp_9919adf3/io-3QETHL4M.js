import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/wallets/__generated__/wallet/io.moonstake/index.js
var wallet = {
  id: "io.moonstake",
  name: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
  homepage: "https://moonstake.io",
  image_id: "22374fae-244c-4224-2e3d-c14912f98a00",
  app: {
    browser: null,
    ios: "https://apps.apple.com/us/app/moonstake-wallet/id1502532651",
    android: "https://play.google.com/store/apps/details?id=io.moonstake.wallet&hl=en",
    mac: null,
    windows: null,
    linux: null,
    chrome: null,
    firefox: null,
    safari: null,
    edge: null,
    opera: null
  },
  rdns: null,
  mobile: {
    native: "moonstake://",
    universal: "https://moonstake.io/launchApp"
  },
  desktop: {
    native: null,
    universal: null
  }
};
export {
  wallet
};
//# sourceMappingURL=io-3QETHL4M.js.map
