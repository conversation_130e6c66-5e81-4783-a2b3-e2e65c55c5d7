import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/wallets/__generated__/wallet/io.kriptomat/index.js
var wallet = {
  id: "io.kriptomat",
  name: "Kriptomat",
  homepage: "https://kriptomat.io/web3/",
  image_id: "774110aa-70f6-4d0c-210f-ab434838fa00",
  app: {
    browser: null,
    ios: "https://apps.apple.com/app/id1440135740",
    android: "https://play.google.com/store/apps/details?id=io.kriptomat.app",
    mac: null,
    windows: null,
    linux: null,
    chrome: null,
    firefox: null,
    safari: null,
    edge: null,
    opera: null
  },
  rdns: null,
  mobile: {
    native: "kriptomatapp://wallet-connect",
    universal: null
  },
  desktop: {
    native: null,
    universal: null
  }
};
export {
  wallet
};
//# sourceMappingURL=io-4LSMJJ5U.js.map
