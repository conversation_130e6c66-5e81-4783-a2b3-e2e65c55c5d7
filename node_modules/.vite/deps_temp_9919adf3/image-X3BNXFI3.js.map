{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/com.coinsdo/image.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nconst image =\n  \"data:image/webp;base64,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\";\n\nexport default image;\n"], "mappings": ";;;AAGA,IAAM,QACJ;AAEF,IAAA,gBAAe;", "names": []}