{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/com.ivirse/image.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nconst image =\n  \"data:image/webp;base64,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\";\n\nexport default image;\n"], "mappings": ";;;AAGA,IAAM,QACJ;AAEF,IAAA,gBAAe;", "names": []}