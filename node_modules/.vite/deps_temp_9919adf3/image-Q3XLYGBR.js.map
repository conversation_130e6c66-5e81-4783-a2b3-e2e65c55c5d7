{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/co.swopme/image.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nconst image =\n  \"data:image/webp;base64,UklGRloDAABXRUJQVlA4WAoAAAAQAAAAfwAAfwAAQUxQSEEBAAABgGNr29LoO7Rwosgi2ERcmGxjKCOLgIpkB+wlUFMltWvpLsQ9bzsTERNAD3oyRas92x++tTtvl42Mh54vhQqmXRuu3e+1GdYsMx8Sn8SnS9UDxooR55/AyPrggLOry8wjnOIcsDoKd59HnR/QzlXPPd7sAXHWe4v9PWBW2WuMMgc1V5grsnNA7cgXvHbArfFEFO8CG6SJROOAvCRRqAKtGvIUDtgLPyY407DA2eUauHp7CG4034Bbuwfw7uHf///+/wO866KbrcFtOiNwQ7sOrla0wVkZE5zpLYArUKgKrRIiqQTNEInSA2DdOBHxGjCNJyKSHViOTJeMMgc1V5grxKqgVJZuenOQcl6606PO4cxVD93NKQ4YR+HoQUbWu0AGuszQ40LcqIColtI8PVcM5U2rNty4X8tdD2u2WQhJdC8AVlA4IPIBAAAQDgCdASqAAIAAPm02mkikI6KhJBn4KIANiWlu4XSRYuXsHgAauZ8Z+RGfD3gi5n7ILplhR8wb6ODkCQEgJASAkBICQEflLjOq6e3F4h9kOiZ6OENnAKvqOr8+aoGt7e/wtAKPPYG4GtK6lSGl2brnpMOJASAkBGAAAP7/kwgBp+wKefewLJXKjccgmTUU9ZNfaNzuNGYGvoAxNY4C9wTjP+iTI7tdXP0Azb2dCYc66SF4BKoobr4gdGMVnkDf48GwmGsad7z5mLAQlM5oUq9/6bLeH6JC/xVAFJ7H7T04lrlD9VOt81xmN8+jUTpYC2v0TYExp7GJkIZeHn+x1/E7F5GsMxD6RfBn6KiFRoZGgZDkd6zWNtZfBZ6VuP7jwqeXyEGvwxKbIP+Dzs7WJf8NGHX6m9xyv8cuN6pypgIE8Ygb/0x6ZEuAvtjnRDJjcHR/JhFMsfglgH/IQMOdo+5Y49HhxJcuf0n3X9zn3KJ/MK/a/VC+gRehSSs2/hb5m7Wi5Pn/Me1SQSNWn3oVe7JFC2TDwBpdTDOQxmD5BaeZuWDUiMLwSuwoQcmK2Lsy8sLspktYSa1zS1nbHGpD/KZRUL7tVHzFgLc4SCwpCo/icj++SPtLRdNbOv3QDTYnvdB9dLtRIkWEO77ON2zhFgAAAAA=\";\n\nexport default image;\n"], "mappings": ";;;AAGA,IAAM,QACJ;AAEF,IAAA,gBAAe;", "names": []}