import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Avatar } from "@/components/ui/avatar";
import { Co<PERSON>, UserIcon, Bo<PERSON> } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useTypingEffect } from "@/hooks/use-typing-effect";
import { useActiveAccount } from "thirdweb/react";

interface ChatMessageProps {
  message: {
    id: number;
    role: string;
    content: string;
    timestamp: string;
    metadata?: {
      chainId?: string;
      source?: string;
      executionTime?: number;
      blockchainData?: any;
    };
  };
}

const ChatMessage = ({ message }: ChatMessageProps) => {
  const { toast } = useToast();
  const isUserMessage = message.role === "user";
  const activeAccount = useActiveAccount();
  const [transactionHash, setTransactionHash] = useState<string | null>(null);

  // Add typing effect for assistant messages
  const { displayedText, isTyping, showFullText } = useTypingEffect(
    !isUserMessage ? message.content : "",
    7 // twice as fast typing speed (was 15ms, now 7ms)
  );

  // Execute transaction function using Web3 browser API
  const executeTransaction = async (txData: any) => {
    if (!window.ethereum) {
      toast({
        title: "No Wallet Found",
        description: "Please install MetaMask or another Web3 wallet",
        variant: "destructive",
      });
      return;
    }

    try {
      // Request wallet connection if needed
      const accounts = await window.ethereum.request({
        method: "eth_requestAccounts",
      });

      if (!accounts || accounts.length === 0) {
        toast({
          title: "Wallet Not Connected",
          description: "Please connect your wallet to execute transactions",
          variant: "destructive",
        });
        return;
      }

      toast({
        title: "Transaction Pending",
        description: "Please confirm the transaction in your wallet...",
      });

      // Send transaction using the browser wallet
      const txHash = await window.ethereum.request({
        method: "eth_sendTransaction",
        params: [
          {
            from: accounts[0],
            to: txData.to,
            value: txData.value,
            data: txData.data || "0x",
          },
        ],
      });

      // Save the transaction hash for the explorer link
      setTransactionHash(txHash);

      toast({
        title: "Transaction Successful!",
        description: `Transaction hash: ${txHash}`,
      });
    } catch (error: any) {
      console.error("Transaction failed:", error);
      toast({
        title: "Transaction Failed",
        description: error.message || "Transaction was rejected or failed",
        variant: "destructive",
      });
    }
  };

  // Format timestamp
  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return `${diffInSeconds}s ago`;
    } else if (diffInSeconds < 3600) {
      return `${Math.floor(diffInSeconds / 60)}m ago`;
    } else if (diffInSeconds < 86400) {
      return `${Math.floor(diffInSeconds / 3600)}h ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  // Parse message content with markdown-like formatting
  const renderContent = (content: string) => {
    // Ensure content is a string
    if (!content || typeof content !== "string") {
      return <span>Loading...</span>;
    }

    // Split by code blocks
    const parts = content.split(/```([\s\S]*?)```/);

    return parts.map((part, index) => {
      if (index % 2 === 1) {
        // Code block
        return (
          <pre
            key={index}
            className="bg-background/50 p-4 rounded-md border border-border my-3 overflow-x-auto"
          >
            <code>{part}</code>
          </pre>
        );
      } else {
        // Regular text with basic formatting
        const formattedText = part
          // Handle lists
          .split("\n")
          .map((line, i) => {
            // Ordered list items
            if (/^\d+\.\s/.test(line)) {
              return `<li class="ml-5">${line.replace(/^\d+\.\s/, "")}</li>`;
            }
            // Unordered list items
            else if (/^-\s/.test(line)) {
              return `<li class="ml-5">${line.replace(/^-\s/, "")}</li>`;
            }
            return line;
          })
          .join("\n")
          // Handle inline code
          .replace(
            /`([^`]+)`/g,
            '<code class="bg-background/50 px-2 py-0.5 rounded text-sm">$1</code>'
          )
          // Handle links
          .replace(
            /\[([^\]]+)\]\(([^)]+)\)/g,
            '<a href="$2" class="text-primary hover:underline">$1</a>'
          )
          // Handle bold
          .replace(/\*\*([^*]+)\*\*/g, "<strong>$1</strong>")
          // Handle paragraphs
          .split("\n\n")
          .map((p) => (p ? `<p>${p}</p>` : ""))
          .join("");

        return (
          <div
            key={index}
            dangerouslySetInnerHTML={{ __html: formattedText }}
            className="space-y-3"
          />
        );
      }
    });
  };

  // Copy message to clipboard
  const copyToClipboard = () => {
    navigator.clipboard.writeText(message.content);
    toast({
      title: "Copied to clipboard",
      description: "The message has been copied to your clipboard.",
      duration: 2000,
    });
  };

  return (
    <div
      className={`fade-in px-4 md:px-8 py-4 ${
        !isUserMessage ? "bg-muted/10" : ""
      } flex`}
    >
      <Avatar
        className={`h-8 w-8 mr-4 mt-1 flex-shrink-0 ${
          isUserMessage ? "bg-muted" : "nebula-icon-bg"
        } flex items-center justify-center`}
      >
        {isUserMessage ? (
          <UserIcon className="h-4 w-4 text-muted-foreground" />
        ) : (
          <Bot className="h-4 w-4 text-foreground" />
        )}
      </Avatar>

      <div className="flex-1">
        <div className="text-foreground">
          {isUserMessage ? (
            <p>{message.content}</p>
          ) : (
            <>
              {renderContent(displayedText)}
              {isTyping && (
                <span className="inline-block w-2 h-5 bg-primary/60 animate-pulse ml-1 align-middle"></span>
              )}
            </>
          )}
        </div>

        {/* Transaction Card - Based on Nebula UI with Real Data */}
        {!isUserMessage &&
          message.metadata?.blockchainData?.actions &&
          message.metadata.blockchainData.actions.length > 0 && (
            <div className="nebula-transaction-card">
              <div className="text-sm font-medium mb-3">
                Transaction Preview
              </div>

              {message.metadata.blockchainData.actions.map(
                (action: any, index: number) => {
                  if (action.type === "sign_transaction" && action.data) {
                    const txData = JSON.parse(action.data);
                    const chainId = txData.chainId;
                    const value = BigInt(txData.value || "0");
                    const formattedValue = (Number(value) / 1e18).toFixed(4);

                    const getChainName = (chainId: number) => {
                      switch (chainId) {
                        case 1:
                          return "Ethereum Mainnet";
                        case 137:
                          return "Polygon Mainnet";
                        case 56:
                          return "BSC Mainnet";
                        case 11155111:
                          return "Sepolia Testnet";
                        case 80002:
                          return "Amoy Testnet";
                        case 97:
                          return "BSC Testnet";
                        default:
                          return `Chain ${chainId}`;
                      }
                    };

                    const getTokenSymbol = (chainId: number) => {
                      switch (chainId) {
                        case 1:
                          return "ETH";
                        case 137:
                          return "POL";
                        case 56:
                          return "BNB";
                        case 11155111:
                          return "ETH";
                        case 80002:
                          return "POL";
                        case 97:
                          return "BNB";
                        default:
                          return "Native";
                      }
                    };

                    return (
                      <div key={index}>
                        {/* From Address */}
                        <div className="nebula-transaction-row">
                          <div className="nebula-transaction-label">From</div>
                          <div className="nebula-transaction-value flex items-center">
                            <div className="w-5 h-5 rounded-full bg-primary/20 flex items-center justify-center mr-2">
                              <UserIcon className="h-3 w-3 text-primary" />
                            </div>
                            <span>Your Wallet</span>
                          </div>
                        </div>

                        {/* To Address */}
                        <div className="nebula-transaction-row">
                          <div className="nebula-transaction-label">To</div>
                          <div className="nebula-transaction-value flex items-center">
                            <div className="w-5 h-5 rounded-full bg-secondary/20 flex items-center justify-center mr-2">
                              <UserIcon className="h-3 w-3 text-primary" />
                            </div>
                            <span>
                              {txData.to.slice(0, 6)}...{txData.to.slice(-4)}
                            </span>
                          </div>
                        </div>

                        {/* Value */}
                        <div className="nebula-transaction-row">
                          <div className="nebula-transaction-label">Value</div>
                          <div className="nebula-transaction-value">
                            {formattedValue} {getTokenSymbol(chainId)}
                          </div>
                        </div>

                        {/* Network */}
                        <div className="nebula-transaction-row">
                          <div className="nebula-transaction-label">
                            Network
                          </div>
                          <div className="nebula-transaction-value flex items-center">
                            <div
                              className={`w-3 h-3 rounded-full mr-2 ${
                                chainId === 1
                                  ? "bg-blue-500"
                                  : chainId === 137
                                  ? "bg-purple-500"
                                  : chainId === 56
                                  ? "bg-yellow-500"
                                  : "bg-gray-500"
                              }`}
                            ></div>
                            <span>{getChainName(chainId)}</span>
                          </div>
                        </div>

                        {/* Transaction Action Buttons */}
                        <div className="mt-4 pt-3 border-t border-border/50">
                          {transactionHash ? (
                            <Button
                              className="w-full bg-green-600 hover:bg-green-700 text-white"
                              onClick={() => {
                                const getExplorerUrl = (
                                  chainId: number,
                                  txHash: string
                                ) => {
                                  switch (chainId) {
                                    case 1:
                                      return `https://etherscan.io/tx/${txHash}`;
                                    case 137:
                                      return `https://polygonscan.com/tx/${txHash}`;
                                    case 56:
                                      return `https://bscscan.com/tx/${txHash}`;
                                    case 11155111:
                                      return `https://sepolia.etherscan.io/tx/${txHash}`;
                                    case 80002:
                                      return `https://amoy.polygonscan.com/tx/${txHash}`;
                                    case 97:
                                      return `https://testnet.bscscan.com/tx/${txHash}`;
                                    default:
                                      return `https://etherscan.io/tx/${txHash}`;
                                  }
                                };
                                window.open(
                                  getExplorerUrl(chainId, transactionHash),
                                  "_blank"
                                );
                              }}
                            >
                              View on Explorer ↗
                            </Button>
                          ) : (
                            <Button
                              className="w-full bg-primary hover:bg-primary/90 text-primary-foreground"
                              onClick={() => executeTransaction(txData)}
                            >
                              Sign & Execute Transaction
                            </Button>
                          )}
                        </div>
                      </div>
                    );
                  }
                  return null;
                }
              )}

              {/* Gas Price Data */}
              {message.metadata.blockchainData.low && (
                <div className="mt-4 mb-2">
                  <div className="text-xs text-muted-foreground mb-2">
                    Current Gas Prices
                  </div>
                  <div className="grid grid-cols-3 gap-2">
                    <div className="bg-muted/30 p-2 rounded-md">
                      <div className="text-xs text-muted-foreground">Low</div>
                      <div className="font-medium text-sm">
                        {message.metadata.blockchainData.low} Gwei
                      </div>
                    </div>
                    <div className="bg-muted/30 p-2 rounded-md">
                      <div className="text-xs text-muted-foreground">
                        Average
                      </div>
                      <div className="font-medium text-sm">
                        {message.metadata.blockchainData.average} Gwei
                      </div>
                    </div>
                    <div className="bg-muted/30 p-2 rounded-md">
                      <div className="text-xs text-muted-foreground">High</div>
                      <div className="font-medium text-sm">
                        {message.metadata.blockchainData.high} Gwei
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

        {/* Message Actions */}
        {!isUserMessage && (
          <div className="mt-3 flex items-center text-xs text-muted-foreground">
            <Button
              variant="ghost"
              size="sm"
              className="hover:text-foreground mr-3 flex items-center gap-1 h-auto p-0"
              onClick={copyToClipboard}
              title="Copy to clipboard"
            >
              <Copy className="h-3.5 w-3.5" />
              <span>Copy</span>
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatMessage;
