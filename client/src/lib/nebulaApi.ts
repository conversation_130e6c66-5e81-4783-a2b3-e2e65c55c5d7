// Nebula API client for chat functionality
// Uses server-side proxy with wallet and chain context

export interface NebulaMessage {
  message: string;
  stream?: boolean;
  session_id?: string;
}

export interface NebulaResponse {
  response: string;
  session_id: string;
  transactions?: any[];
}

export interface NebulaContextFilter {
  walletAddresses?: string[];
  chainIds?: number[];
  contractAddresses?: string[];
}

/**
 * Send a message to Nebula Chat API via server proxy with wallet and chain context
 */
export async function sendNebulaMessage(
  message: string,
  contextFilter?: NebulaContextFilter,
  sessionId?: string,
  stream: boolean = false
): Promise<NebulaResponse> {
  const payload = {
    message,
    stream,
    session_id: sessionId,
    contextFilter,
  };

  try {
    const response = await fetch("/api/nebula/chat", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Nebula API error: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error calling Nebula API:", error);
    throw error;
  }
}

/**
 * Send a streaming message to Nebula Chat API via server proxy
 */
export async function sendNebulaMessageStream(
  message: string,
  contextFilter?: NebulaContextFilter,
  sessionId?: string,
  onChunk?: (chunk: string) => void,
  onComplete?: (metadata: any) => void,
  onError?: (error: string) => void
): Promise<void> {
  const payload = {
    message,
    session_id: sessionId,
    contextFilter,
  };

  try {
    const response = await fetch("/api/nebula/chat/stream", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Nebula API error: ${response.status} - ${errorText}`);
    }

    // Handle Server-Sent Events
    if (response.body) {
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = "";

      try {
        while (true) {
          const { done, value } = await reader.read();

          if (done) {
            break;
          }

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split("\n");
          buffer = lines.pop() || ""; // Keep incomplete line in buffer

          for (const line of lines) {
            if (line.startsWith("data: ")) {
              try {
                const data = JSON.parse(line.slice(6));

                if (data.type === "chunk" && data.content) {
                  onChunk?.(data.content);
                } else if (data.type === "complete") {
                  onComplete?.(data);
                } else if (data.type === "error") {
                  onError?.(data.error);
                }
              } catch (parseError) {
                console.warn("Failed to parse streaming data:", line);
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }
    }
  } catch (error) {
    console.error("Error calling Nebula streaming API:", error);
    onError?.(error instanceof Error ? error.message : "Unknown error");
    throw error;
  }
}

/**
 * Generate a new session ID for Nebula chat
 */
export function generateSessionId(): string {
  return crypto.randomUUID();
}

/**
 * Get or create a session ID for the current user
 */
export function getOrCreateSessionId(): string {
  const storageKey = "nebula_session_id";
  let sessionId = localStorage.getItem(storageKey);

  if (!sessionId) {
    sessionId = generateSessionId();
    localStorage.setItem(storageKey, sessionId);
  }

  return sessionId;
}
